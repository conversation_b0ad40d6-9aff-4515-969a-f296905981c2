import { <PERSON>umn, DeleteDateColumn, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { transformHashId } from '../../../helpers/hash.helper';
import { BaseIdEntity } from '../../../bases/BaseId.entity';
import { ProductEntity } from './Product.entity';
import { WarehouseStockEntity } from '../../warehouses/entities/WarehouseStock.entity';
import { TransferGoodProductEntity } from '../../transfer-goods/entities/TransferGoodsProduct.entity';
import { OrderProductEntity } from '../../orders/entities/OrderProduct.entity';
import { SupplierProductEntity } from '../../supplier-products/entities/SupplierProduct.entity';
import { PartnerProductChildrenEntity } from './PartnerProductChildren.entity';

@Entity('product_children')
export class ProductChildrenEntity extends BaseIdEntity {
    @Column()
    product_child_id: string;

    @Column()
    title: string;

    @Column()
    price: string;

    @Column()
    color: string;

    @Column()
    compare_at_price: string;

    @Column()
    barcode: string;

    @Column()
    sku: string;

    @Column()
    inventory_item_id: string;

    @Column()
    inventory_quantity: number;

    @Column({
        type: 'integer',
        nullable: true,
        transformer: {
            to(value) {
                return transformHashId(value, true);
            },
            from(value) {
                return transformHashId(value, false);
            },
        },
    })
    product_id: number | string;

    @Column({ type: 'jsonb' })
    designs: any;

    @Column()
    image: string;

    @DeleteDateColumn({ nullable: true, default: null })
    deleted_at?: Date | null;

    @Column({ length: 2 })
    preset: string;

    @Column({ type: 'numeric', precision: 5, scale: 2 })
    partner_price: number;

    @Column({ type: 'smallint' })
    exist_status_id: ExistStatus;

    @Column({
        type: 'integer',
        nullable: true,
        transformer: {
            to(value) {
                return transformHashId(value, true);
            },
            from(value) {
                return transformHashId(value, false);
            },
        },
    })
    root_id: number | string;

    @ManyToOne(() => ProductEntity, (p) => p.children, { orphanedRowAction: 'soft-delete' })
    @JoinColumn({ name: 'product_id' })
    product: ProductEntity;

    @OneToMany(() => WarehouseStockEntity, (ws) => ws.variant)
    stocks: WarehouseStockEntity[];

    @OneToMany(() => OrderProductEntity, (op) => op.productChild)
    orderProducts: OrderProductEntity[];

    @OneToMany(() => OrderProductEntity, (op) => op.designProductChild)
    orderProductDesigns: OrderProductEntity[];

    @OneToMany(() => TransferGoodProductEntity, (tgp) => tgp.productChild)
    transferGoodProducts: TransferGoodProductEntity[];

    @OneToMany(() => SupplierProductEntity, (sp) => sp.productChild)
    supplierProducts: SupplierProductEntity[];

    @OneToMany(() => PartnerProductChildrenEntity, (d) => d.partner)
    ppcs: PartnerProductChildrenEntity[];

    @OneToMany(() => 'OrderProductEntity', (op: any) => op.rootProductChild)
    orderProductRoots: any[];

    @ManyToOne(() => ProductChildrenEntity, (pc) => pc.children)
    @JoinColumn({ name: 'root_id' })
    root: ProductChildrenEntity;

    @OneToMany(() => ProductChildrenEntity, (pc) => pc.root)
    children: ProductChildrenEntity[];
}

export enum ExistStatus {
    OLD = 1,
    DELETED = 2,
    NEW = 3,
}
