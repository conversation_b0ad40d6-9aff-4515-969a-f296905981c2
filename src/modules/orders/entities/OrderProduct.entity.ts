import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { transformHashId } from '../../../helpers/hash.helper';
import { BaseEntity } from '../../../bases/Base.entity';
import { OrderEntity } from './Order.entity';
import { ProductChildrenEntity } from '../../products/entitties/ProductChildren.entity';
import { Exclude } from 'class-transformer';

@Entity('order_products')
export class OrderProductEntity extends BaseEntity {
    @Column({
        type: 'integer',
        nullable: true,
        default: null,
        transformer: {
            to(value) {
                return transformHashId(value, true);
            },
            from(value) {
                return transformHashId(value, false);
            },
        },
    })
    order_id: number | string;

    @Column({
        type: 'integer',
        nullable: true,
        default: null,
        transformer: {
            to(value) {
                return transformHashId(value, true);
            },
            from(value) {
                return transformHashId(value, false);
            },
        },
    })
    product_child_id: number | string;

    @Column()
    quantity: number;

    @Column()
    quantity_draft: number;

    @Column()
    designed_quantity: number;

    @Column({ type: 'smallint' })
    status_id: OrderProductStatus;

    @Column({ type: 'numeric' })
    price: number;

    @Column({
        type: 'integer',
        nullable: true,
        default: null,
        transformer: {
            to(value) {
                return transformHashId(value, true);
            },
            from(value) {
                return transformHashId(value, false);
            },
        },
    })
    designed_product_child_id: number | string;

    @Column({
        type: 'integer',
        nullable: true,
        default: null,
        transformer: {
            to(value) {
                return transformHashId(value, true);
            },
            from(value) {
                return transformHashId(value, false);
            },
        },
    })
    root_product_child_id: number | string;

    @ManyToOne(() => OrderEntity, (o) => o.products, { orphanedRowAction: 'soft-delete' })
    @JoinColumn({ name: 'order_id' })
    order: OrderEntity;

    @ManyToOne(() => ProductChildrenEntity, (pc) => pc.orderProducts)
    @JoinColumn({ name: 'product_child_id' })
    productChild: ProductChildrenEntity;

    @ManyToOne(() => ProductChildrenEntity, (pc) => pc.orderProductDesigns)
    @JoinColumn({ name: 'designed_product_child_id' })
    designProductChild: ProductChildrenEntity;

    @Exclude()
    search_text: string[] = ['productChild.product.title', 'productChild.title', 'productChild.sku'];
}

export enum OrderProductStatus {
    PENDING = 1,
    MANUFACTURING = 2,
    FINISH = 3,
}
