import { MigrationInterface, QueryRunner, <PERSON>C<PERSON>umn, Table<PERSON>oreign<PERSON><PERSON> } from "typeorm";

export class AddRootProductChildIdToOrderProductsTable1752591000000 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn(
            'order_products',
            new TableColumn({
                name: 'root_product_child_id',
                type: 'int',
                isNullable: true,
            })
        );

        await queryRunner.createForeignKey(
            'order_products',
            new TableForeignKey({
                columnNames: ['root_product_child_id'],
                referencedColumnNames: ['id'],
                referencedTableName: 'product_children',
                onUpdate: 'CASCADE',
                onDelete: 'SET NULL',
                name: 'fk_order_products_root_product_child_id',
            })
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropForeignKey('order_products', 'fk_order_products_root_product_child_id');
        await queryRunner.dropColumn('order_products', 'root_product_child_id');
    }

}
